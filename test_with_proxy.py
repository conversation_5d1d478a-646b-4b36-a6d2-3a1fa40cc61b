import requests
import time
from concurrent.futures import ThreadPoolExecutor
import os

# 代理配置
PROXY_ENABLED = True  # 设置为True启用代理，False禁用代理
PROXY_HOST = "**************"  # 代理服务器地址
PROXY_PORT = 7890  # mihomo默认端口
PROXY_AUTH = ('proxy','Yyh12345Xyz')  # 如果需要认证，设置为 ('username', 'password')

# 配置代理
def get_proxies():
    """获取代理配置"""
    if not PROXY_ENABLED:
        return None
    
    proxy_url = f"http://{PROXY_HOST}:{PROXY_PORT}"
    
    # 如果需要认证
    if PROXY_AUTH:
        username, password = PROXY_AUTH
        proxy_url = f"http://{username}:{password}@{PROXY_HOST}:{PROXY_PORT}"
    
    return {
        'http': proxy_url,
        'https': proxy_url
    }

def test_proxy_connection():
    """测试代理连接是否正常"""
    if not PROXY_ENABLED:
        print("代理未启用，跳过代理测试")
        return True
    
    print(f"测试代理连接: {PROXY_HOST}:{PROXY_PORT}")
    try:
        # 使用一个简单的URL测试代理
        test_url = "http://httpbin.org/ip"
        proxies = get_proxies()
        
        # 先不使用代理获取IP
        response_direct = requests.get(test_url, timeout=5)
        direct_ip = response_direct.json().get('origin', 'Unknown')
        print(f"直连IP: {direct_ip}")
        
        # 使用代理获取IP
        response_proxy = requests.get(test_url, proxies=proxies, timeout=5)
        proxy_ip = response_proxy.json().get('origin', 'Unknown')
        print(f"代理IP: {proxy_ip}")
        
        if direct_ip != proxy_ip:
            print("✓ 代理连接正常，IP已改变")
        else:
            print("⚠ 警告：代理IP与直连IP相同，代理可能未生效")
        
        return True
    except Exception as e:
        print(f"✗ 代理连接失败: {e}")
        return False

def test_api_status():
    """测试东方财富API状态码并统计成功率"""
    # API URL
    url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
    
    # 请求参数
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6',
        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f116',
        'ut': '7eea3edcaed734bea9cbfc24409ed989',
        'klt': '101',
        'fqt': '0',
        'secid': '1.000001',
        'beg': '20250102',
        'end': '20250103'
    }
    
    # 获取代理配置
    proxies = get_proxies()
    
    def fetch(i):
        """发送单个请求并返回状态码"""
        try:
            response = requests.get(
                url, 
                params=params, 
                proxies=proxies,
                timeout=10,  # 使用代理时增加超时时间
                verify=True  # 验证SSL证书
            )
            return response.status_code
        except requests.exceptions.ProxyError as e:
            print(f"请求 {i+1} 代理错误: {e}")
            return None
        except requests.exceptions.SSLError as e:
            print(f"请求 {i+1} SSL错误: {e}")
            return None
        except requests.exceptions.Timeout as e:
            print(f"请求 {i+1} 超时: {e}")
            return None
        except requests.exceptions.RequestException as e:
            print(f"请求 {i+1} 失败: {e}")
            return None
        except Exception as e:
            print(f"请求 {i+1} 出现异常: {e}")
            return None
    
    # 测试次数
    test_count = 100
    print(f"\n开始测试，共 {test_count} 次请求...")
    print(f"目标URL: {url}")
    print(f"代理状态: {'启用' if PROXY_ENABLED else '禁用'}")
    if PROXY_ENABLED:
        print(f"代理地址: {PROXY_HOST}:{PROXY_PORT}")
    print("-" * 60)
    
    start_time = time.time()
    
    # 使用线程池并发执行请求
    with ThreadPoolExecutor(max_workers=10) as executor:
        results = list(executor.map(fetch, range(test_count)))
    
    # 统计结果
    success_count = sum(1 for r in results if r == 200)
    failed_count = sum(1 for r in results if r is None)
    other_status_count = sum(1 for r in results if r is not None and r != 200)
    
    # 统计不同状态码的分布
    status_codes = {}
    for r in results:
        if r is not None:
            status_codes[r] = status_codes.get(r, 0) + 1
    
    # 计算耗时
    elapsed_time = time.time() - start_time
    
    # 输出结果
    print(f"\n测试结果:")
    print(f"总请求数: {test_count}")
    print(f"成功数 (状态码200): {success_count}")
    print(f"失败数 (请求异常): {failed_count}")
    print(f"其他状态码: {other_status_count}")
    print(f"成功率: {(success_count/test_count)*100:.2f}%")
    print(f"总耗时: {elapsed_time:.2f} 秒")
    print(f"平均响应时间: {(elapsed_time/test_count)*1000:.2f} 毫秒")
    
    # 显示状态码分布
    if status_codes:
        print(f"\n状态码分布:")
        for code, count in sorted(status_codes.items()):
            percentage = (count / test_count) * 100
            print(f"  状态码 {code}: {count} 次 ({percentage:.2f}%)")
    
    # 详细的结果列表（可选）
    print(f"\n详细结果 (前20个):")
    for i, result in enumerate(results[:20], 1):
        if result is None:
            print(f"  {i:3d}. 请求失败")
        else:
            status_text = "成功" if result == 200 else f"状态码 {result}"
            print(f"  {i:3d}. {status_text}")
    
    if test_count > 20:
        print(f"  ... (剩余 {test_count - 20} 个结果省略)")
    
    return success_count, test_count

def test_single_request():
    """测试单个请求，查看详细响应"""
    print("\n单个请求测试:")
    print("-" * 60)
    
    url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
    params = {
        'fields1': 'f1,f2,f3,f4,f5,f6',
        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f116',
        'ut': '7eea3edcaed734bea9cbfc24409ed989',
        'klt': '101',
        'fqt': '0',
        'secid': '1.000001',
        'beg': '20250102',
        'end': '20250103'
    }
    
    # 获取代理配置
    proxies = get_proxies()
    
    try:
        start = time.time()
        response = requests.get(
            url, 
            params=params, 
            proxies=proxies,
            timeout=10,
            verify=True
        )
        elapsed = time.time() - start
        
        print(f"URL: {response.url[:100]}...")
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {elapsed*1000:.2f} 毫秒")
        print(f"响应头 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"响应大小: {len(response.content)} 字节")
        print(f"代理状态: {'使用代理' if PROXY_ENABLED else '直连'}")
        
        if response.status_code == 200:
            print("✓ 请求成功!")
            # 尝试解析JSON
            try:
                data = response.json()
                print(f"响应数据类型: JSON")
                if 'data' in data:
                    print(f"数据记录数: {len(data.get('data', {}).get('klines', []))} 条")
            except:
                print("响应数据类型: 非JSON格式")
        else:
            print(f"✗ 请求返回非200状态码: {response.status_code}")
            
    except requests.exceptions.ProxyError as e:
        print(f"✗ 代理错误: {e}")
    except requests.exceptions.SSLError as e:
        print(f"✗ SSL错误: {e}")
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
    except requests.exceptions.ConnectionError as e:
        print(f"✗ 连接错误: {e}")
    except requests.exceptions.RequestException as e:
        print(f"✗ 请求异常: {e}")
    except Exception as e:
        print(f"✗ 未知错误: {e}")

def main():
    """主函数"""
    print("东方财富API代理测试工具")
    print("=" * 60)
    
    # 首先测试代理连接
    if PROXY_ENABLED:
        if not test_proxy_connection():
            print("\n代理连接失败，请检查:")
            print("1. mihomo是否正在运行")
            print("2. 代理端口是否正确 (默认7890)")
            print("3. 如果设置了认证，请检查用户名密码")
            print("\n您可以将 PROXY_ENABLED 设置为 False 来禁用代理直接测试")
            return
    
    # 测试单个请求
    test_single_request()
    
    print("\n" + "=" * 60)
    
    # 批量测试
    test_api_status()

if __name__ == "__main__":
    main()