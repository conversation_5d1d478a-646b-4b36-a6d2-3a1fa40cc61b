services:
  metacubexd:
    container_name: metacubexd
    image: ghcr.io/metacubex/metacubexd:latest
    restart: always
    ports:
      - '2080:80'
    networks:
      - mihomo-network

  mihomo:
    container_name: mihomo
    image: docker.io/metacubex/mihomo:latest
    restart: always
    networks:
      - mihomo-network
    ports:
      - '7890:7890'
      - '9090:9090'
    cap_add:
      - ALL
    volumes:
      - ./config.yaml:/root/.config/mihomo/config.yaml
      - /dev/net/tun:/dev/net/tun

networks:
  mihomo-network:
    driver: bridge