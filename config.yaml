# ========== 代理端口配置 ==========
mixed-port: 7890

# ========== 网络基础配置 ==========
allow-lan: true          
bind-address: "*"        
mode: global            
ipv6: false             

# ========== 性能优化配置 ==========
tcp-concurrent: true
unified-delay: false     # 关闭统一延迟，爬虫不需要延迟测试
keep-alive-idle: 30      # 缩短空闲时间，快速释放连接
keep-alive-interval: 10  # 缩短保活间隔

find-process-mode: off   
profile:
  store-selected: false  

experimental:
  ignore-resolve-fail: true  
  sniff-tls-sni: false      

log-level: silent

# ========== 订阅源配置 ==========
proxy-providers:
  provider1:
    type: http
    url: "https://subapi.proxygo.de/share/file/mihomo?token=2RcX5VjR6zLRKv3Ufyqhm"
    interval: 3600
    health-check:
      enable: true
      interval: 30       # 缩短到30秒，更快剔除故障节点
      url: http://www.gstatic.com/generate_204
      timeout: 3000      # 增加超时，部分节点响应慢
      lazy: false      # 改为主动健康检查，确保节点状态实时更新
      expected-status: 204
    override:
      udp: false
      skip-cert-verify: true

# ========== 负载均衡配置 ==========
proxy-groups:
  # 主负载均衡组
  - name: 负载均衡
    type: load-balance
    use:
      - provider1
    proxies: []
    strategy: round-robin  # 轮询：每次请求强制切换IP
    interval: 30      # 缩短到30秒，提高节点切换频率
    lazy: false       # 改为主动健康检查，配合轮询策略使用
    timeout: 3000     # 增加超时容忍度
    max-failed-times: 3  # 新增：失败3次后剔除节点
    url: http://www.gstatic.com/generate_204
    exclude-filter: "(?i)游戏|game|直连|剩余|流量|过期|官网|到期"
    disable-udp: true       # 新增：禁用UDP，只使用TCP

  - name: GLOBAL
    type: select
    proxies:
      - 负载均衡

# ========== DNS配置 ==========
dns:
  enable: true
  listen: 0.0.0.0:53
  ipv6: false
  default-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  enhanced-mode: fake-ip    # 改为fake-ip，爬虫不需要真实IP
  cache-algorithm: arc
  cache-size: 10000         # 增大DNS缓存，减少DNS查询

# ========== TUN 模式配置 ==========
tun:
  enable: true          # 自动启用 TUN 模式
  stack: gvisor         # 明确指定使用 gVisor 网络堆栈
  dns-hijack:           # 劫持 DNS 请求，确保 DNS 流量也通过 Mihomo
    - any:53
  auto-route: true      # 自动设置系统路由
  auto-detect-interface: true # 自动检测出口网卡

# ========== 连接池优化 ==========
pool:
  size: 2000            # 增大连接池，支持更多并发
  idle-timeout: 60      # 缩短空闲超时，快速回收

# ========== 并发控制 ==========
global-max-connections: 2000  # 增大最大连接数，爬虫需要高并发

# ========== 内存优化 ==========
test-concurrent: 100    # 增大测试并发数，快速筛选可用节点

# ========== 规则配置 ==========
rules:
  - MATCH,负载均衡

# ========== 控制面板 ==========
external-controller: 0.0.0.0:9090
secret: "Yyh12345@"